﻿namespace ProjectManagement.API.Controllers
{
    // 测试生成相关扩展
    public partial class AIController
    {
        #region 测试生成辅助方法

        /// <summary>
        /// 获取项目信息用于测试生成
        /// </summary>
        private async Task<ProjectInfoForTestGeneration> GetProjectInfoForTestGeneration(int projectId)
        {
            try
            {
                // TODO: 实现获取项目详细信息的逻辑
                // 这里应该从数据库获取项目的需求文档、架构信息等

                return new ProjectInfoForTestGeneration
                {
                    ProjectId = projectId,
                    ProjectName = $"项目_{projectId}",
                    Specification = "项目规格说明文档内容...",
                    Architecture = "项目架构描述...",
                    TechnologyStack = "技术栈信息...",
                    DatabaseSchema = "数据库架构..."
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取项目信息失败: ProjectId={ProjectId}", projectId);
                throw;
            }
        }

        /// <summary>
        /// 获取代码内容用于测试生成
        /// </summary>
        private async Task<string> GetCodeContentForTesting(int projectId, int? codeGenerationTaskId)
        {
            try
            {
                if (codeGenerationTaskId.HasValue)
                {
                    // TODO: 从代码生成任务中获取生成的代码
                    // var generatedCode = await _codeGenerationService.GetGeneratedCodeAsync(codeGenerationTaskId.Value);
                    // return generatedCode;
                }

                // TODO: 从项目中获取现有代码文件
                // var projectCode = await _projectService.GetProjectCodeAsync(projectId);
                // return projectCode;

                return "// 项目代码内容\n// TODO: 实现从项目获取代码的逻辑";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取代码内容失败: ProjectId={ProjectId}, TaskId={TaskId}", projectId, codeGenerationTaskId);
                return "// 无法获取代码内容";
            }
        }

        /// <summary>
        /// 保存生成的测试用例
        /// </summary>
        private async Task<List<SavedTestCase>> SaveGeneratedTestCases(int projectId, string testCasesJson, string testType, string testFramework)
        {
            var savedTests = new List<SavedTestCase>();

            try
            {
                // 解析AI生成的测试用例JSON
                var testCases = System.Text.Json.JsonSerializer.Deserialize<List<GeneratedTestCase>>(testCasesJson);

                if (testCases != null)
                {
                    foreach (var testCase in testCases)
                    {
                        // TODO: 保存到数据库
                        // var savedTest = await _testCaseRepository.CreateAsync(new TestCase
                        // {
                        //     ProjectId = projectId,
                        //     Name = testCase.Name,
                        //     Description = testCase.Description,
                        //     TestType = testType,
                        //     Framework = testFramework,
                        //     Code = testCase.Code,
                        //     Priority = testCase.Priority ?? "Medium",
                        //     Tags = testCase.Tags != null ? string.Join(",", testCase.Tags) : "",
                        //     CreatedTime = DateTime.UtcNow
                        // });

                        savedTests.Add(new SavedTestCase
                        {
                            Id = savedTests.Count + 1, // 临时ID
                            Name = testCase.Name ?? "未命名测试",
                            Description = testCase.Description ?? "",
                            Code = testCase.Code ?? "",
                            TestType = testType,
                            Framework = testFramework
                        });
                    }
                }

                _logger.LogInformation("保存测试用例完成: ProjectId={ProjectId}, Count={Count}", projectId, savedTests.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存测试用例失败: ProjectId={ProjectId}", projectId);

                // 即使保存失败，也返回一个默认的测试用例
                savedTests.Add(new SavedTestCase
                {
                    Id = 1,
                    Name = "AI生成的测试用例",
                    Description = "测试用例保存失败，但AI生成成功",
                    Code = testCasesJson,
                    TestType = testType,
                    Framework = testFramework
                });
            }

            return savedTests;
        }

        /// <summary>
        /// 获取用户的AI配置
        /// </summary>
        private async Task<ProjectManagement.Core.DTOs.AI.AIModelConfig> GetAIConfigForUser(int userId, string? preferredModel = null)
        {
            try
            {
                var aiConfig = await ProjectManagement.API.Helper.AIConfigurationHelper.GetUserAIConfigurationAsync(
                    _aiConfigRepository,
                    _logger,
                    userId,
                    null, // aiProviderConfigId
                    4000, // defaultMaxTokens
                    0.7f, // defaultTemperature
                    _encryptionService);

                if (aiConfig == null)
                {
                    // 返回默认配置
                    return new ProjectManagement.Core.DTOs.AI.AIModelConfig
                    {
                        Provider = "DeepSeek",
                        Model = preferredModel ?? "deepseek-chat",
                        ApiKey = "",
                        Endpoint = "https://api.deepseek.com",
                        MaxTokens = 4000,
                        Temperature = 0.7f
                    };
                }

                return aiConfig;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户AI配置失败: UserId={UserId}", userId);

                // 返回默认配置
                return new ProjectManagement.Core.DTOs.AI.AIModelConfig
                {
                    Provider = "DeepSeek",
                    Model = preferredModel ?? "deepseek-chat",
                    ApiKey = "",
                    Endpoint = "https://api.deepseek.com",
                    MaxTokens = 4000,
                    Temperature = 0.7f
                };
            }
        }

        #endregion

        #region 测试生成相关数据模型

        /// <summary>
        /// 项目测试生成信息
        /// </summary>
        public class ProjectInfoForTestGeneration
        {
            public int ProjectId { get; set; }
            public string ProjectName { get; set; } = string.Empty;
            public string? Specification { get; set; }
            public string? Architecture { get; set; }
            public string? TechnologyStack { get; set; }
            public string? DatabaseSchema { get; set; }
        }

        /// <summary>
        /// AI生成的测试用例
        /// </summary>
        public class GeneratedTestCase
        {
            public string? Name { get; set; }
            public string? Description { get; set; }
            public string? Code { get; set; }
            public string? Priority { get; set; }
            public List<string>? Tags { get; set; }
            public string? Category { get; set; }
        }

        /// <summary>
        /// 保存的测试用例
        /// </summary>
        public class SavedTestCase
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Code { get; set; } = string.Empty;
            public string TestType { get; set; } = string.Empty;
            public string Framework { get; set; } = string.Empty;
        }

        /// <summary>
        /// 测试生成结果DTO
        /// </summary>
        public class TestGenerationResultDto
        {
            public bool Success { get; set; }
            public string Message { get; set; } = string.Empty;
            public List<TestCaseDto> TestCases { get; set; } = new();
            public string? GeneratedContent { get; set; }
        }

        /// <summary>
        /// 测试用例DTO
        /// </summary>
        public class TestCaseDto
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Code { get; set; } = string.Empty;
            public string TestType { get; set; } = string.Empty;
            public string Framework { get; set; } = string.Empty;
            public string? Priority { get; set; }
            public List<string>? Tags { get; set; }
            public DateTime? CreatedTime { get; set; }
        }

        #endregion

    }
}
